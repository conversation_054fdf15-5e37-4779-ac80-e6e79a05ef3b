import { Address } from '@experience/commercial/site-admin/typescript/domain-model';
import {
  Charger,
  CommissioningCertificate,
} from '@experience/support/support-tool/shared';
import { ChargerService } from '../charger.service';
import { CommissioningCertificateNotAvailable } from './commissioning.exception';
import { Door } from '@experience/shared/axios/assets-configuration-api-client';
import { Injectable } from '@nestjs/common';
import { OidcUser } from '@experience/shared/nest/utils';
import dayjs from 'dayjs';

@Injectable()
export class ChargerCommissioningService {
  constructor(private readonly chargerService: ChargerService) {}

  async getCommissioningCertificate(
    ppid: string,
    user: OidcUser
  ): Promise<CommissioningCertificate> {
    const chargerData = await this.chargerService.findByPpid(
      ppid,
      Door.A,
      false,
      user
    );

    return this.determineCommissioningStatus(chargerData);
  }

  private determineCommissioningStatus(
    chargerData: Charger
  ): CommissioningCertificate {
    const isDomestic = chargerData.summary?.location?.type === 'DOMESTIC';
    const hasCommercialAttributes = !!chargerData.commercialAttributes;

    if (!hasCommercialAttributes || isDomestic) {
      throw new CommissioningCertificateNotAvailable();
    }

    const address = chargerData.commercialAttributes?.site?.address;

    return {
      ppid: chargerData.summary?.ppid || '-',
      chargerName: chargerData.name || 'Unknown',
      address: address ? this.formatAddressWithPostcode(address) : '-',
      group: this.getGroup(chargerData),
      adminEmailAddress: this.getAdminEmailAddress(chargerData),
      connectivityStatusOnline: this.isChargerOnline(chargerData),
      isPublic: this.isChargerPublic(chargerData),
      confirmChargeEnabled: this.hasConfirmChargeEnabled(chargerData),
      hasTariff: this.hasTariffAssigned(chargerData),
      warrantyStartDate: this.getWarrantyStartDate(chargerData),
      warrantyEndDate: this.getWarrantyEndDate(chargerData),
      generatedAt: new Date().toISOString(),
      installedBy: this.getInstalledBy(chargerData),
      company: this.getCompany(chargerData),
    };
  }

  private isChargerOnline(chargerData: Charger): boolean {
    const evses = chargerData.connectivityStatus?.evses;

    if (!evses || evses.length === 0) {
      return false;
    }

    // All EVSEs must be online for the charger to be considered communicating
    return evses.every(
      (evse) => evse.connectivityState?.connectivityStatus === 'ONLINE'
    );
  }

  private isChargerPublic(chargerData: Charger): boolean {
    return chargerData.summary?.location?.isPublic ?? false;
  }

  private hasConfirmChargeEnabled(chargerData: Charger): boolean {
    const confirmChargeConfig =
      chargerData.configuration?.configuration?.configurationKey?.find(
        (config) => config.key === 'ConfirmCharge'
      );

    if (confirmChargeConfig) {
      return confirmChargeConfig.value === 'true';
    }

    return chargerData.commercialAttributes?.settings?.confirmCharge ?? false;
  }

  private hasTariffAssigned(chargerData: Charger): boolean {
    return !!chargerData.commercialAttributes?.tariff;
  }

  private formatAddressWithPostcode(address: Address): string {
    if (!address) return '-';
    const parts = [
      address.line1,
      address.line2,
      address.town,
      address.postcode,
    ].filter(Boolean);
    return parts.join(', ');
  }

  private getWarrantyStartDate(chargerData: Charger): string {
    if (!chargerData.salesforceAsset?.Warranty_Start_Date__c) return '-';
    return dayjs(chargerData.salesforceAsset?.Warranty_Start_Date__c).format(
      'DD/MM/YYYY'
    );
  }

  private getWarrantyEndDate(chargerData: Charger): string {
    if (!chargerData.salesforceAsset?.Warranty_End_Date__c) return '-';
    return dayjs(chargerData.salesforceAsset?.Warranty_End_Date__c).format(
      'DD/MM/YYYY'
    );
  }

  private getAdminEmailAddress(chargerData: Charger): string {
    return chargerData.commercialAttributes?.admins?.[0]?.email || '-';
  }

  private getInstalledBy(chargerData: Charger): string {
    const installation = chargerData.installationDetails?.find(
      (install) => (install.socket ?? 'A') === 'A'
    );

    if (installation?.user?.firstName && installation?.user?.lastName) {
      return `${installation.user.firstName} ${installation.user.lastName}`;
    }
    return '-';
  }

  private getCompany(chargerData: Charger): string {
    const installation = chargerData.installationDetails?.find(
      (install) => (install.socket ?? 'A') === 'A'
    );
    return installation?.user?.companyName || '-';
  }

  private getGroup(chargerData: Charger): string {
    return chargerData.commercialAttributes?.group?.name || '-';
  }
}
