import {
  CommissioningCertificateNotAvailable,
  CommissioningErrorCodes,
} from './commissioning.exception';
import { HttpInterceptor } from '@experience/shared/nest/utils';
import { HttpStatus, Injectable } from '@nestjs/common';

@Injectable()
export class CommissioningInterceptor extends HttpInterceptor {
  constructor() {
    super([
      {
        code: CommissioningErrorCodes.COMMISSIONING_CERTIFICATE_NOT_AVAILABLE,
        name: CommissioningCertificateNotAvailable,
        statusCode: HttpStatus.NOT_FOUND,
      },
    ]);
  }
}
