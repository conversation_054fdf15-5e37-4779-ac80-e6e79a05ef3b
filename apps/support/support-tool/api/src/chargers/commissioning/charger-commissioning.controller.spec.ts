import {
  AMAZON_OIDC_DATA_HEADER,
  getOidcUser,
} from '@experience/shared/typescript/oidc-utils';
import { APP_GUARD } from '@nestjs/core';
import { ChargerCommissioningController } from './charger-commissioning.controller';
import { ChargerCommissioningService } from './charger-commissioning.service';
import {
  CommissioningCertificate,
  OidcRoles,
} from '@experience/support/support-tool/shared';
import { CommissioningCertificateNotAvailable } from './commissioning.exception';
import { INestApplication } from '@nestjs/common';
import { RolesGuard, useGlobalPipes } from '@experience/shared/nest/utils';
import {
  TEST_OIDC_DATA,
  TEST_OIDC_USER,
} from '@experience/shared/typescript/oidc-utils/specs';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';

jest.mock('./charger-commissioning.service');
const TEST_PPID = 'PSL-123456';

jest.mock('@experience/shared/typescript/oidc-utils');
const mockGetOidcUser = jest.mocked(getOidcUser);

const TEST_OIDC_USER_WITH_ROLE = {
  ...TEST_OIDC_USER,
  roles: [OidcRoles.CHARGER_COMMISSIONING],
};

const mockCertificate: CommissioningCertificate = {
  ppid: 'PSL-123456',
  chargerName: 'Test Charger',
  address: 'Test Address',
  group: 'Test Group',
  adminEmailAddress: '<EMAIL>',
  connectivityStatusOnline: true,
  isPublic: true,
  confirmChargeEnabled: true,
  hasTariff: true,
  warrantyStartDate: '2023-01-01',
  warrantyEndDate: '2025-01-01',
  installedBy: 'Test Installer',
  company: 'Test Company',
  generatedAt: '2024-01-01T00:00:00.000Z',
};

describe('ChargerCommissioningController', () => {
  let app: INestApplication;
  let controller: ChargerCommissioningController;
  let service: ChargerCommissioningService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChargerCommissioningController],
      providers: [
        { provide: APP_GUARD, useClass: RolesGuard },
        ChargerCommissioningService,
      ],
    }).compile();

    controller = module.get<ChargerCommissioningController>(
      ChargerCommissioningController
    );
    service = module.get<ChargerCommissioningService>(
      ChargerCommissioningService
    );

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();
  });

  beforeEach(() => {
    mockGetOidcUser.mockReturnValue(TEST_OIDC_USER_WITH_ROLE);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should return commissioning certificate', async () => {
    const mockGetCommissioningCertificate = jest
      .spyOn(service, 'getCommissioningCertificate')
      .mockResolvedValueOnce(mockCertificate);

    await request(app.getHttpServer())
      .get(`/chargers/${TEST_PPID}/commissioning/certificate`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200);

    expect(mockGetCommissioningCertificate).toHaveBeenCalledWith(
      TEST_PPID,
      TEST_OIDC_USER_WITH_ROLE
    );
  });

  it('should handle service errors', async () => {
    const error = new CommissioningCertificateNotAvailable();
    jest
      .spyOn(service, 'getCommissioningCertificate')
      .mockRejectedValueOnce(error);

    await request(app.getHttpServer())
      .get(`/chargers/${TEST_PPID}/commissioning/certificate`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(404);
  });

  it('should return 403 forbidden when user does not have the required role', async () => {
    mockGetOidcUser.mockReturnValueOnce(TEST_OIDC_USER);
    const mockGetCommissioningCertificate = jest.spyOn(
      service,
      'getCommissioningCertificate'
    );

    await request(app.getHttpServer())
      .get(`/chargers/${TEST_PPID}/commissioning/certificate`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(403)
      .expect({
        statusCode: 403,
        message: 'Forbidden resource',
        error: 'Forbidden',
      });

    expect(mockGetCommissioningCertificate).not.toHaveBeenCalled();
  });
});
