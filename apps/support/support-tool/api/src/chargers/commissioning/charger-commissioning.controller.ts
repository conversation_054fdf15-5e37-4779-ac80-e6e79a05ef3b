import { ChargerCommissioningService } from './charger-commissioning.service';
import {
  CommissioningCertificate,
  OidcRoles,
} from '@experience/support/support-tool/shared';
import { CommissioningInterceptor } from './commissioning.interceptor';
import { Controller, Get, Param, UseInterceptors } from '@nestjs/common';
import { Has<PERSON><PERSON><PERSON>, OidcUser } from '@experience/shared/nest/utils';

@Controller('chargers')
@UseInterceptors(CommissioningInterceptor)
export class ChargerCommissioningController {
  constructor(
    private readonly chargerCommissioningService: ChargerCommissioningService
  ) {}

  @Get(':ppid/commissioning/certificate')
  @HasRoles(OidcRoles.CHARGER_COMMISSIONING)
  async getCommissioningCertificate(
    @Param('ppid') ppid: string,
    @OidcUser()
    user: OidcUser
  ): Promise<CommissioningCertificate> {
    return this.chargerCommissioningService.getCommissioningCertificate(
      ppid,
      user
    );
  }
}
