import 'whatwg-fetch';
import { Charger, OidcRoles } from '@experience/support/support-tool/shared';
import { ChargerCommissioningService } from './charger-commissioning.service';
import { ChargerService } from '../charger.service';
import { CommissioningCertificateNotAvailable } from './commissioning.exception';
import { OidcUser } from '@experience/shared/nest/utils';
import { TEST_OIDC_USER } from '@experience/shared/typescript/oidc-utils/specs';
import { Test, TestingModule } from '@nestjs/testing';
import {
  mockCharger,
  mockCommercialCharger,
} from '@experience/support/support-tool/shared/specs';

describe('ChargerCommissioningService', () => {
  let service: ChargerCommissioningService;
  let chargerService: jest.Mocked<ChargerService>;

  const mockUser: OidcUser = {
    ...TEST_OIDC_USER,
    roles: [OidcRoles.CHARGER_COMMISSIONING],
  };

  beforeEach(async () => {
    const mockChargerService = {
      findByPpid: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChargerCommissioningService,
        {
          provide: ChargerService,
          useValue: mockChargerService,
        },
      ],
    }).compile();

    service = module.get<ChargerCommissioningService>(
      ChargerCommissioningService
    );
    chargerService = module.get(ChargerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getCommissioningCertificate', () => {
    it('should return commissioning certificate', async () => {
      chargerService.findByPpid.mockResolvedValue(mockCommercialCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result).toEqual({
        address:
          'allatus degero clarus, dolores appositus decumbo, denego possimus solitudo, constans aestus triumphus',
        adminEmailAddress: '<EMAIL>',
        chargerName: 'Test-Name',
        company: 'Electrical Services Ltd.',
        confirmChargeEnabled: true,
        connectivityStatusOnline: false,
        generatedAt: expect.any(String),
        group: 'Test group',
        hasTariff: true,
        installedBy: 'Joe Spark',
        isPublic: true,
        ppid: 'PG-12345',
        warrantyEndDate: '01/08/2027',
        warrantyStartDate: '01/08/2024',
      });
    });

    it('should handle offline charger', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        summary: { ppid: 'PSL-123456' },
        name: 'Test Charger',
        connectivityStatus: {
          evses: [
            {
              id: 1,
              connectivityState: {
                connectivityStatus: 'OFFLINE',
              },
            },
          ],
        },
      } as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.connectivityStatusOnline).toBe(false);
    });

    it('should handle charger with all EVSEs online', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        connectivityStatus: {
          evses: [
            {
              id: 1,
              connectivityState: {
                connectivityStatus: 'ONLINE',
              },
            },
            {
              id: 2,
              connectivityState: {
                connectivityStatus: 'ONLINE',
              },
            },
          ],
        },
      } as unknown as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.connectivityStatusOnline).toBe(true);
    });

    it('should handle charger with mixed EVSE connectivity', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        connectivityStatus: {
          evses: [
            {
              id: 1,
              connectivityState: {
                connectivityStatus: 'ONLINE',
              },
            },
            {
              id: 2,
              connectivityState: {
                connectivityStatus: 'OFFLINE',
              },
            },
          ],
        },
      } as unknown as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.connectivityStatusOnline).toBe(false);
    });

    it('should handle charger with no EVSEs', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        connectivityStatus: {
          evses: [],
        },
      } as unknown as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.connectivityStatusOnline).toBe(false);
    });

    it('should handle private charger', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        summary: {
          ...mockCommercialCharger.summary,
          location: {
            type: 'PRIVATE',
          },
        },
      } as unknown as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.isPublic).toBe(false);
    });

    it('should handle charger without confirm charge (Arch5)', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        configuration: {
          configuration: {
            configurationKey: [{ key: 'ConfirmCharge', value: 'false' }],
          },
        },
      } as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.confirmChargeEnabled).toBe(false);
    });

    it('should handle charger with confirm charge enabled (Arch5)', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        configuration: {
          configuration: {
            configurationKey: [{ key: 'ConfirmCharge', value: 'true' }],
          },
        },
      } as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.confirmChargeEnabled).toBe(true);
    });

    it('should handle charger with confirm charge enabled (non-Arch5)', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        commercialAttributes: {
          settings: {
            confirmCharge: true,
          },
        },
      } as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.confirmChargeEnabled).toBe(true);
    });

    it('should handle charger without confirm charge (non-Arch5)', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        commercialAttributes: {
          settings: {
            confirmCharge: false,
          },
        },
      } as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.confirmChargeEnabled).toBe(false);
    });

    it('should handle charger with tariff assigned', async () => {
      chargerService.findByPpid.mockResolvedValue(mockCommercialCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.hasTariff).toBe(true);
    });

    it('should handle charger without tariff', async () => {
      const testCharger = {
        ...mockCommercialCharger,
        commercialAttributes: {
          ...mockCommercialCharger.commercialAttributes,
          tariff: null,
        },
      } as unknown as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.hasTariff).toBe(false);
    });

    it('should throw error for non-commercial charger without commercial attributes', async () => {
      const testCharger = {
        ...mockCharger,
        commercialAttributes: undefined,
        summary: {
          ...mockCharger.summary,
          location: {
            type: 'COMMERCIAL',
          },
        },
      } as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      await expect(
        service.getCommissioningCertificate('PSL-123456', mockUser)
      ).rejects.toThrow(CommissioningCertificateNotAvailable);
    });

    it('should throw error for domestic charger with commercial attributes', async () => {
      const testCharger = {
        ...mockCharger,
        summary: {
          ...mockCharger.summary,
          location: {
            type: 'DOMESTIC',
          },
        },
      } as unknown as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      await expect(
        service.getCommissioningCertificate('PSL-123456', mockUser)
      ).rejects.toThrow(CommissioningCertificateNotAvailable);
    });

    it('should throw error for domestic charger without commercial attributes', async () => {
      const testCharger = {
        ...mockCharger,
        commercialAttributes: undefined,
        summary: {
          ...mockCharger.summary,
          location: {
            type: 'DOMESTIC',
          },
        },
      } as unknown as Charger;

      chargerService.findByPpid.mockResolvedValue(testCharger);

      await expect(
        service.getCommissioningCertificate('PSL-123456', mockUser)
      ).rejects.toThrow(CommissioningCertificateNotAvailable);
    });
  });
});
