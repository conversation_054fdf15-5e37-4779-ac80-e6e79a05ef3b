import {
  Card,
  Heading,
  HeadingSizes,
} from '@experience/shared/react/design-system';
import { CommissioningCertificate } from '@experience/support/support-tool/shared';
import { useTranslations } from 'next-intl';

interface CommissioningCertificateProps {
  certificate: CommissioningCertificate;
}

export const CommissioningCertificateComponent = ({
  certificate,
}: CommissioningCertificateProps) => {
  const t = useTranslations('Chargers.CommissioningCertificatePage');

  return (
    <Card className="p-8">
      <div className="text-center mb-8">
        <Heading.H1 fontSize={HeadingSizes.XL} className="text-navy mb-4">
          {t('certificateTitle')}
        </Heading.H1>
        <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
      </div>

      <div className="mb-8">
        <div className="bg-primary text-white p-4 mb-4">
          <div className="flex justify-between items-center">
            <span className="font-semibold">{certificate.chargerName}</span>
            <span className="text-sm">{certificate.ppid}</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <div className="border border-smoke p-3">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('ppidLabel')}
              </div>
              <div className="text-charcoal">{certificate.ppid}</div>
            </div>
          </div>
          <div>
            <div className="border border-smoke p-3">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('addressLabel')}
              </div>
              <div className="text-charcoal">{certificate.address}</div>
            </div>
          </div>
          <div>
            <div className="border border-smoke p-3">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('groupLabel')}
              </div>
              <div className="text-charcoal">{certificate.group}</div>
            </div>
          </div>
          <div>
            <div className="border border-smoke p-3">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('adminEmailLabel')}
              </div>
              <div className="text-charcoal">
                {certificate.adminEmailAddress}
              </div>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <div className="space-y-3">
            <div className="flex justify-between items-center py-2 border-b border-smoke">
              <span className="text-charcoal">
                {t('communicatingStatement')}
              </span>
              <span className="font-semibold text-navy">
                {certificate.connectivityStatusOnline ? t('yes') : t('no')}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-smoke">
              <span className="text-charcoal">{t('appearOnAppStatement')}</span>
              <span className="font-semibold text-navy">
                {certificate.isPublic ? t('yes') : t('no')}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-smoke">
              <span className="text-charcoal">
                {t('authenticatedViaAppStatement')}
              </span>
              <span className="font-semibold text-navy">
                {certificate.confirmChargeEnabled ? t('yes') : t('no')}
              </span>
            </div>
            <div className="flex justify-between items-center py-2 border-b border-smoke">
              <span className="text-charcoal">{t('paygTariffStatement')}</span>
              <span className="font-semibold text-navy">
                {certificate.hasTariff ? t('yes') : t('no')}
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <div className="border border-smoke p-3">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('warrantyStartDateLabel')}
              </div>
              <div className="text-charcoal">
                {certificate.warrantyStartDate}
              </div>
            </div>
          </div>
          <div>
            <div className="border border-smoke p-3">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('warrantyEndDateLabel')}
              </div>
              <div className="text-charcoal">{certificate.warrantyEndDate}</div>
            </div>
          </div>
          <div>
            <div className="border border-smoke p-3">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('installedByLabel')}
              </div>
              <div className="text-charcoal">{certificate.installedBy}</div>
            </div>
          </div>
          <div>
            <div className="border border-smoke p-3">
              <div className="font-semibold text-sm text-gunmetal mb-1">
                {t('installerCompanyLabel')}
              </div>
              <div className="text-charcoal">{certificate.company}</div>
            </div>
          </div>
        </div>
      </div>

      <div className="border-t border-smoke pt-6 text-center">
        <p className="text-sm text-gunmetal">
          {t('generatedOnText', {
            date: new Date(certificate.generatedAt).toLocaleDateString('en-GB'),
            time: new Date(certificate.generatedAt).toLocaleTimeString('en-GB'),
          })}
        </p>
      </div>
    </Card>
  );
};
