import {
  CommissioningCertificate,
  OidcRoles,
} from '@experience/support/support-tool/shared';

jest.mock('./page-content', () => ({
  __esModule: true,
  default: (certificate: CommissioningCertificate) => (
    <div data-testid="page-content">Certificate for {certificate.ppid}</div>
  ),
}));

jest.mock('@experience/commercial/next/app-request-utils', () => ({
  appRequestHandler: jest.fn().mockResolvedValue({
    ppid: 'PSL-123456',
    chargerName: 'Test Charger',
    address: 'Test Address, Test Town',
    postcode: 'SW1A 1AA',
    adminEmailAddress: '<EMAIL>',
    isCurrentlyCommunicating: true,
    isSetToAppearOnApp: true,
    isSetToBeAuthenticatedViaApp: true,
    hasPaygTariffSet: true,
    warrantyStartDate: '15/01/2024',
    warrantyEndDate: '15/01/2027',
    generatedAt: '2023-01-01T00:00:00.000Z',
  }),
}));

const mockNotFound = jest.fn();
jest.mock('next/navigation', () => ({
  notFound: () => mockNotFound(),
}));

const mockCookies = jest.fn();
jest.mock('next/headers', () => ({
  cookies: () => mockCookies(),
}));

import { renderWithProviders } from '../../../../test-utils';
import CommissioningCertificatePage from './page';

describe('CommissioningCertificatePage', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Default: user has the required role
    mockCookies.mockReturnValue({
      get: jest.fn().mockReturnValue({
        value: OidcRoles.CHARGER_COMMISSIONING,
      }),
    });
  });

  it('should render correctly when user has the required role', async () => {
    const props = {
      params: Promise.resolve({ ppid: 'PSL-123456' }),
    };
    const Component = await CommissioningCertificatePage(props);
    const { baseElement, getByTestId } = renderWithProviders(Component);

    expect(baseElement).toBeInTheDocument();
    expect(getByTestId('page-content')).toBeInTheDocument();
    expect(mockNotFound).not.toHaveBeenCalled();
  });

  it('should match snapshot when user has the required role', async () => {
    const props = {
      params: Promise.resolve({ ppid: 'PSL-123456' }),
    };
    const Component = await CommissioningCertificatePage(props);
    const { baseElement } = renderWithProviders(Component);

    expect(baseElement).toMatchSnapshot();
  });

  it('should return 404 when user does not have the required role', async () => {
    mockCookies.mockReturnValue({
      get: jest.fn().mockReturnValue({
        value: 'SomeOtherRole',
      }),
    });

    const props = {
      params: Promise.resolve({ ppid: 'PSL-123456' }),
    };

    await CommissioningCertificatePage(props);

    expect(mockNotFound).toHaveBeenCalled();
  });

  it('should return 404 when user has no roles', async () => {
    mockCookies.mockReturnValue({
      get: jest.fn().mockReturnValue(undefined),
    });

    const props = {
      params: Promise.resolve({ ppid: 'PSL-123456' }),
    };

    await CommissioningCertificatePage(props);

    expect(mockNotFound).toHaveBeenCalled();
  });

  it('should allow access when user has multiple roles including the required one', async () => {
    mockCookies.mockReturnValue({
      get: jest.fn().mockReturnValue({
        value: `SomeOtherRole,${OidcRoles.CHARGER_COMMISSIONING},AnotherRole`,
      }),
    });

    const props = {
      params: Promise.resolve({ ppid: 'PSL-123456' }),
    };
    const Component = await CommissioningCertificatePage(props);
    const { getByTestId } = renderWithProviders(Component);

    expect(getByTestId('page-content')).toBeInTheDocument();
    expect(mockNotFound).not.toHaveBeenCalled();
  });
});
