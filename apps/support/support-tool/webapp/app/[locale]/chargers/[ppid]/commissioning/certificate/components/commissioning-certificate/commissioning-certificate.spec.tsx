import { CommissioningCertificate } from '@experience/support/support-tool/shared';
import { CommissioningCertificateComponent } from './commissioning-certificate';
import { renderWithProviders } from '../../../../../../test-utils';
import { screen } from '@testing-library/react';

describe('CommissioningCertificateComponent', () => {
  const mockCertificate: CommissioningCertificate = {
    ppid: 'PSL-123456',
    chargerName: 'Test Charger',
    address: 'Test Address, Test Town, SW1A 1AA',
    group: 'Test Group',
    adminEmailAddress: '<EMAIL>',
    connectivityStatusOnline: true,
    isPublic: true,
    confirmChargeEnabled: true,
    hasTariff: true,
    warrantyStartDate: '15/01/2024',
    warrantyEndDate: '15/01/2027',
    generatedAt: '2023-01-01T00:00:00.000Z',
    installedBy: '<PERSON>',
    company: 'Test Company',
  };

  it('should render successfully', () => {
    const { baseElement } = renderWithProviders(
      <CommissioningCertificateComponent certificate={mockCertificate} />
    );

    expect(baseElement).toBeInTheDocument();
    expect(screen.getByText('Commissioning Certificate')).toBeInTheDocument();
    expect(screen.getByText('Test Charger')).toBeInTheDocument();
    expect(screen.getAllByText('PSL-123456')).toHaveLength(2);
  });

  it('should display charger information', () => {
    renderWithProviders(
      <CommissioningCertificateComponent certificate={mockCertificate} />
    );

    expect(screen.getByText('PPID number:')).toBeInTheDocument();
    expect(screen.getByText('Address:')).toBeInTheDocument();
    expect(screen.getByText('Group:')).toBeInTheDocument();
    expect(
      screen.getByText('Test Address, Test Town, SW1A 1AA')
    ).toBeInTheDocument();
    expect(screen.getByText('Test Group')).toBeInTheDocument();
  });

  it('should display commissioning status checks', () => {
    renderWithProviders(
      <CommissioningCertificateComponent certificate={mockCertificate} />
    );

    expect(
      screen.getByText(
        'The unit installed in this project is currently communicating'
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'The unit installed in this project is set to appear on the app'
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'The unit installed in this project is set to be authenticated via the app'
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        'The unit installed in this project has been set a PAYG tariff'
      )
    ).toBeInTheDocument();
  });

  it('should show Yes/No status indicators', () => {
    renderWithProviders(
      <CommissioningCertificateComponent certificate={mockCertificate} />
    );

    const yesElements = screen.getAllByText('Yes');
    expect(yesElements).toHaveLength(4);
  });

  it('should display warranty information', () => {
    renderWithProviders(
      <CommissioningCertificateComponent certificate={mockCertificate} />
    );

    expect(screen.getByText('Warranty Start Date:')).toBeInTheDocument();
    expect(screen.getByText('Warranty End Date:')).toBeInTheDocument();
    expect(screen.getByText('15/01/2024')).toBeInTheDocument();
    expect(screen.getByText('15/01/2027')).toBeInTheDocument();
  });

  it('should display installation information', () => {
    renderWithProviders(
      <CommissioningCertificateComponent certificate={mockCertificate} />
    );

    expect(screen.getByText('Installed by:')).toBeInTheDocument();
    expect(screen.getByText('Installer Company:')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Test Company')).toBeInTheDocument();
  });

  it('should display generation date and time', () => {
    renderWithProviders(
      <CommissioningCertificateComponent certificate={mockCertificate} />
    );

    expect(screen.getByText(/Generated on/)).toBeInTheDocument();
  });

  it('should show No for failed checks', () => {
    const failedCertificate: CommissioningCertificate = {
      ...mockCertificate,
      connectivityStatusOnline: false,
      isPublic: false,
      confirmChargeEnabled: false,
      hasTariff: false,
    };

    renderWithProviders(
      <CommissioningCertificateComponent certificate={failedCertificate} />
    );

    const noElements = screen.getAllByText('No');
    expect(noElements).toHaveLength(4);
  });

  it('should match snapshot', () => {
    const { baseElement } = renderWithProviders(
      <CommissioningCertificateComponent certificate={mockCertificate} />
    );

    expect(baseElement).toMatchSnapshot();
  });
});
