import {
  CommissioningCertificate,
  OidcRoles,
} from '@experience/support/support-tool/shared';
import { Metadata } from 'next';
import {
  MetadataProps,
  generatePageMetadata,
} from '@experience/support/support-tool/next';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { notFound } from 'next/navigation';
import { userHasRole } from '../../../../roles/user-role-check';
import PageContent from './page-content';

export const generateMetadata = async (
  props: MetadataProps
): Promise<Metadata> => {
  const params = await props.params;

  return await generatePageMetadata({
    namespace: 'Chargers.CommissioningCertificatePage',
    ppid: params.ppid,
  });
};

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const CommissioningCertificatePage = async (props: {
  params: Promise<{ ppid: string }>;
}) => {
  const params = await props.params;

  if (!userHasRole(OidcRoles.CHARGER_COMMISSIONING)) {
    return notFound();
  }

  const certificate = await appRequestHandler<CommissioningCertificate>(
    `${process.env.SUPPORT_TOOL_API_URL}/chargers/${params.ppid}/commissioning/certificate`
  );

  return <PageContent certificate={certificate} ppid={params.ppid} />;
};

export default CommissioningCertificatePage;
