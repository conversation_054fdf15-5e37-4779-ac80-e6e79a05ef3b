// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`CommissioningCertificateComponent should match snapshot 1`] = `
<body>
  <div>
    <section
      class="rounded-lg bg-white p-8"
    >
      <div
        class="text-center mb-8"
      >
        <h1
          class="text-3xl text-navy mb-4"
        >
          Commissioning Certificate
        </h1>
        <div
          class="w-24 h-1 bg-primary mx-auto mb-6"
        />
      </div>
      <div
        class="mb-8"
      >
        <div
          class="bg-primary text-white p-4 mb-4"
        >
          <div
            class="flex justify-between items-center"
          >
            <span
              class="font-semibold"
            >
              Test Charger
            </span>
            <span
              class="text-sm"
            >
              PSL-123456
            </span>
          </div>
        </div>
        <div
          class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"
        >
          <div>
            <div
              class="border border-smoke p-3"
            >
              <div
                class="font-semibold text-sm text-gunmetal mb-1"
              >
                PPID number:
              </div>
              <div
                class="text-charcoal"
              >
                PSL-123456
              </div>
            </div>
          </div>
          <div>
            <div
              class="border border-smoke p-3"
            >
              <div
                class="font-semibold text-sm text-gunmetal mb-1"
              >
                Address:
              </div>
              <div
                class="text-charcoal"
              >
                Test Address, Test Town, SW1A 1AA
              </div>
            </div>
          </div>
          <div>
            <div
              class="border border-smoke p-3"
            >
              <div
                class="font-semibold text-sm text-gunmetal mb-1"
              >
                Group:
              </div>
              <div
                class="text-charcoal"
              >
                Test Group
              </div>
            </div>
          </div>
          <div>
            <div
              class="border border-smoke p-3"
            >
              <div
                class="font-semibold text-sm text-gunmetal mb-1"
              >
                Admin Email Address:
              </div>
              <div
                class="text-charcoal"
              >
                <EMAIL>
              </div>
            </div>
          </div>
        </div>
        <div
          class="mb-6"
        >
          <div
            class="space-y-3"
          >
            <div
              class="flex justify-between items-center py-2 border-b border-smoke"
            >
              <span
                class="text-charcoal"
              >
                The unit installed in this project is currently communicating
              </span>
              <span
                class="font-semibold text-navy"
              >
                Yes
              </span>
            </div>
            <div
              class="flex justify-between items-center py-2 border-b border-smoke"
            >
              <span
                class="text-charcoal"
              >
                The unit installed in this project is set to appear on the app
              </span>
              <span
                class="font-semibold text-navy"
              >
                Yes
              </span>
            </div>
            <div
              class="flex justify-between items-center py-2 border-b border-smoke"
            >
              <span
                class="text-charcoal"
              >
                The unit installed in this project is set to be authenticated via the app
              </span>
              <span
                class="font-semibold text-navy"
              >
                Yes
              </span>
            </div>
            <div
              class="flex justify-between items-center py-2 border-b border-smoke"
            >
              <span
                class="text-charcoal"
              >
                The unit installed in this project has been set a PAYG tariff
              </span>
              <span
                class="font-semibold text-navy"
              >
                Yes
              </span>
            </div>
          </div>
        </div>
        <div
          class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6"
        >
          <div>
            <div
              class="border border-smoke p-3"
            >
              <div
                class="font-semibold text-sm text-gunmetal mb-1"
              >
                Warranty Start Date:
              </div>
              <div
                class="text-charcoal"
              >
                15/01/2024
              </div>
            </div>
          </div>
          <div>
            <div
              class="border border-smoke p-3"
            >
              <div
                class="font-semibold text-sm text-gunmetal mb-1"
              >
                Warranty End Date:
              </div>
              <div
                class="text-charcoal"
              >
                15/01/2027
              </div>
            </div>
          </div>
          <div>
            <div
              class="border border-smoke p-3"
            >
              <div
                class="font-semibold text-sm text-gunmetal mb-1"
              >
                Installed by:
              </div>
              <div
                class="text-charcoal"
              >
                John Doe
              </div>
            </div>
          </div>
          <div>
            <div
              class="border border-smoke p-3"
            >
              <div
                class="font-semibold text-sm text-gunmetal mb-1"
              >
                Installer Company:
              </div>
              <div
                class="text-charcoal"
              >
                Test Company
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="border-t border-smoke pt-6 text-center"
      >
        <p
          class="text-sm text-gunmetal"
        >
          Generated on 01/01/2023 at 00:00:00
        </p>
      </div>
    </section>
  </div>
</body>
`;
