'use client';

import {
  <PERSON><PERSON>,
  ArrowLeftIcon,
  Button,
  ButtonTypes,
} from '@experience/shared/react/design-system';
import { CommissioningCertificate } from '@experience/support/support-tool/shared';
import { CommissioningCertificateComponent } from './components/commissioning-certificate/commissioning-certificate';
import { useTranslations } from 'next-intl';

interface PageContentProps {
  certificate: CommissioningCertificate;
  ppid: string;
}

const PageContent = ({ certificate, ppid }: PageContentProps) => {
  const t = useTranslations('Chargers.CommissioningCertificatePage');

  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="space-y-6">
      <Anchor
        className="flex items-center gap-1 mb-2 font-bold max-w-fit print:hidden"
        href={`/chargers/${ppid}`}
      >
        <ArrowLeftIcon.LIGHT width="w-4 h-4" />
        {t('backButton')}
      </Anchor>
      <div className="flex items-center justify-end">
        <div className="flex gap-3 print:hidden">
          <Button buttonType={ButtonTypes.PRIMARY} onClick={handlePrint}>
            {t('printButton')}
          </Button>
        </div>
      </div>
      <CommissioningCertificateComponent certificate={certificate} />
    </div>
  );
};

export default PageContent;
