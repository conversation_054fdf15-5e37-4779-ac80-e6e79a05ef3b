import { CommissioningCertificate } from '@experience/support/support-tool/shared';
import { fireEvent, screen } from '@testing-library/react';
import { renderWithProviders } from '../../../../test-utils';
import PageContent from './page-content';

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
}));

const mockCertificate: CommissioningCertificate = {
  address: 'Test Address, Test Town, SW1A 1AA',
  adminEmailAddress: '<EMAIL>',
  chargerName: 'Test Charger',
  company: 'Test Company',
  confirmChargeEnabled: true,
  connectivityStatusOnline: true,
  generatedAt: '2023-01-01T00:00:00.000Z',
  group: 'Test Group',
  hasTariff: true,
  installedBy: '<PERSON>',
  isPublic: true,
  ppid: 'PSL-123456',
  warrantyEndDate: '15/01/2027',
  warrantyStartDate: '15/01/2024',
};

describe('CommissioningCertificatePageContent', () => {
  const mockPrint = jest.fn();

  beforeEach(() => {
    Object.defineProperty(window, 'print', {
      value: mockPrint,
      writable: true,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    const { baseElement } = renderWithProviders(
      <PageContent certificate={mockCertificate} ppid="PSL-123456" />
    );

    expect(baseElement).toBeInTheDocument();
    expect(screen.getByText('Print Certificate')).toBeInTheDocument();
    expect(screen.getByText('Back')).toBeInTheDocument();
  });

  it('should handle print button click', () => {
    renderWithProviders(
      <PageContent certificate={mockCertificate} ppid="PSL-123456" />
    );

    const printButton = screen.getByText('Print Certificate');
    fireEvent.click(printButton);

    expect(mockPrint).toHaveBeenCalledTimes(1);
  });

  it('should display generated date', () => {
    renderWithProviders(
      <PageContent certificate={mockCertificate} ppid="PSL-123456" />
    );

    expect(screen.getByText(/Generated on/)).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = renderWithProviders(
      <PageContent certificate={mockCertificate} ppid="PSL-123456" />
    );

    expect(baseElement).toMatchSnapshot();
  });
});
