import { OidcRoles } from '@experience/support/support-tool/shared';
import { cookies } from 'next/headers';
import { userHasRole } from './user-role-check';

jest.mock('next/headers');
const mockCookies = jest.mocked(cookies);

describe('User Role Check', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return true if user has the specified role', () => {
    mockCookies.mockReturnValue({
      get: jest.fn().mockReturnValue({
        value: `${OidcRoles.CHARGER_COMMISSIONING},OtherRole`,
      }),
    } as never);

    const result = userHasRole(OidcRoles.CHARGER_COMMISSIONING);
    expect(result).toBe(true);
  });

  it('should return false if user does not have the specified role', () => {
    mockCookies.mockReturnValue({
      get: jest.fn().mockReturnValue({
        value: 'OtherRole,AnotherRole',
      }),
    } as never);

    const result = userHasRole(OidcRoles.CHARGER_COMMISSIONING);
    expect(result).toBe(false);
  });

  it('should return false if no roles cookie exists', () => {
    mockCookies.mockReturnValue({
      get: jest.fn().mockReturnValue(undefined),
    } as never);

    const result = userHasRole(OidcRoles.CHARGER_COMMISSIONING);
    expect(result).toBe(false);
  });

  it('should return false if roles cookie has no value', () => {
    mockCookies.mockReturnValue({
      get: jest.fn().mockReturnValue({ value: '' }),
    } as never);

    const result = userHasRole(OidcRoles.CHARGER_COMMISSIONING);
    expect(result).toBe(false);
  });
});
