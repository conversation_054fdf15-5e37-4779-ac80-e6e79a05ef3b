import {
  ALLOWED_ROLES,
  OidcRoles,
} from '@experience/support/support-tool/shared';
import { cookies } from 'next/headers';

export const userHasRole = (role: OidcRoles): boolean => {
  const rolesCookie = cookies().get('x-amzn-oidc-data-roles');
  const roles =
    rolesCookie?.value
      .split(',')
      .filter((role) => ALLOWED_ROLES.includes(role as OidcRoles)) ?? [];

  return roles.includes(role);
};
