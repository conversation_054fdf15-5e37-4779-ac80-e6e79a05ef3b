package charges

import (
	"context"
	"experience/libs/data-platform/cost-calculation/revenue"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/billing"
	revenue2 "experience/libs/data-platform/event-sourcing/domain/events/charges/revenue"
	"experience/libs/shared/go/numbers"
	"experience/libs/shared/go/service/domain"
	"experience/libs/shared/go/sqs"
	"fmt"
	"log"

	"github.com/google/uuid"
	"k8s.io/utils/ptr"
)

type billChargeServiceImpl struct {
	logger                *log.Logger
	aggregateLoader       AggregateLoader
	msgHandler            sqs.MessageHandler
	sqsClient             sqs.ClientOperations
	revenueCalcRepository revenue2.Repository
	revenueCalculator     revenue.Calculator
}

func NewBillChargeService(logger *log.Logger, aggregateLoader AggregateLoader, msgHandler sqs.MessageHandler, sqsClient sqs.ClientOperations, revenueCalcRepository revenue2.Repository, revenueCalculator revenue.Calculator) BillChargeService {
	return &billChargeServiceImpl{
		logger:                logger,
		aggregateLoader:       aggregateLoader,
		msgHandler:            msgHand<PERSON>,
		sqsClient:             sqsClient,
		revenueCalcRepository: revenueCalcRepository,
		revenueCalculator:     revenueCalculator,
	}
}

func (s *billChargeServiceImpl) BillCharge(ctx context.Context, aggregateID uuid.UUID) error {
	aggregate, err := s.aggregateLoader.LoadCharge(ctx, aggregateID)
	if err != nil {
		return fmt.Errorf("failed to load charge: %s, %w", aggregateID, err)
	}

	// AuthoriserType is mapped to aggregate by completed event
	// We are here because of a Charge.Confirmed event which in turn happened because of a Charge.Completed event.
	// Therefore, it is safe to expect the AuthoriserType to be mapped to the aggregate.
	// It could just as easily be retrieved from the Charge.Completed event though.
	authoriserType := ptr.Deref(aggregate.AuthoriserType, "")
	billingType := billing.TypeUnsupported
	switch authoriserType {
	case User.String():
		billingType = billing.TypeWallet
	case Guest.String():
		billingType = billing.TypeGuest
	case OCPI.String():
		billingType = billing.TypeOCPI
	}

	completedEvent, err := aggregate.FindCompletedEvent()
	if err != nil {
		return fmt.Errorf("failed to find completed event: %s, %w", aggregateID, err)
	}

	if billingType == billing.TypeGuest && completedEvent.Payload.Billing != nil {
		s.logger.Printf("Billing data already present for Guest billing: %s", aggregateID)
		return nil
	}

	settlement := s.getSettlement(ctx, billingType, completedEvent)
	settlementAmount, err := numbers.Convert[int, int32](settlement.Amount)
	if err != nil {
		return fmt.Errorf("error converting settlement amount: %w", err)
	}

	billed := NewBilled(
		aggregateID,
		settlementAmount,
		settlement.Currency,
		billingType,
	)

	return processDirectWithSQSFallback(ctx, &billed, s.msgHandler, s.sqsClient)
}

func (s *billChargeServiceImpl) getSettlement(ctx context.Context, billingType billing.Type, completedEvent *Completed) domain.Money {
	// If this is Wallet billing we use calculated revenue
	// If there is no Authoriser ID then revenue calculation will fail, the Bill command will be DLQed and the Charge.Billed event will not exist
	// This is OK as we can't support Wallet billing without an Authoriser ID
	if billingType == billing.TypeWallet || billingType == billing.TypeOCPI || billingType == billing.TypeGuest {
		return s.calculateRevenue(ctx, completedEvent)
	}
	// If this is NOT Wallet billing we definitely expect the Billing section of the Completed event to exist.
	// When these were mapped to the aggregate as part of Costing the charge, we would have abandoned it if Billing information was not present,
	// so a failure here would have the same effect, but with a Bill command in the DLQ.
	return domain.Money{
		Amount:   completedEvent.Payload.Billing.SettlementAmount,
		Currency: completedEvent.Payload.Billing.SettlementCurrency,
	}
}

func (s *billChargeServiceImpl) calculateRevenue(ctx context.Context, completedEvent *Completed) domain.Money {
	locationID := completedEvent.Payload.Location.ID

	authoriserID := *completedEvent.Payload.Authorisation.AuthoriserID
	userType, err := s.revenueCalcRepository.GetUserTypeByLocationAndAuthoriserPK(ctx, locationID, authoriserID)
	if err != nil {
		s.logger.Printf("RevenueCalculation - retrieving user type: %v", err)
	}

	revenueTiers := s.getRevenueTiers(ctx, locationID, userType)

	locale, err := s.revenueCalcRepository.GetRevenueProfileLocaleByLocation(ctx, locationID)
	if err != nil {
		s.logger.Printf("RevenueCalculation - retrieving revenue profile locale: %v", err)
	}

	profile := revenue.Profile{Tiers: revenueTiers}
	charge := toCostCalcCharge(&completedEvent.Payload)
	charge.Location = locale.Location
	calculatedAmount := s.revenueCalculator.Revenue(profile, charge)

	return domain.Money{
		Amount:   calculatedAmount,
		Currency: locale.Currency,
	}
}

func (s *billChargeServiceImpl) getRevenueTiers(ctx context.Context, locationID int, userType revenue.UserType) []revenue.Tier {
	revenueTiers, err := s.revenueCalcRepository.GetRevenueProfileTiersByLocationAndUserType(ctx, locationID, userType)
	if err != nil {
		s.logger.Printf("RevenueCalculation - retrieving revenue profile tiers: %v", err)
	}

	return revenueTiers
}
