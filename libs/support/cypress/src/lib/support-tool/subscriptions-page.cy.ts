export const describeSubscriptionsPage = (): void => {
  describe('Subscriptions page', () => {
    it('should render correctly', () => {
      cy.clickLink('Subscriptions');
      cy.enterText('Email or PPID', '<EMAIL>');
      cy.clickButton('Go');
      cy.clickButton('Open');
      cy.shouldHaveHeading(1, 'Pod Drive Status');

      cy.findAllByText('Pay upfront fee').should('exist');
      cy.findAllByText('Survey completed').should('exist');
      cy.findAllByText('Affordability passed').should('exist');
      cy.findAllByText('Direct debit set up').should('exist');
      cy.findAllByText('Legal documents signed').should('exist');
      cy.findAllByText('Charging station installed').should('exist');
    });
  });
};
