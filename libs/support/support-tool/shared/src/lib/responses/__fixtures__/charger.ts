import {
  ChargeOverrideResponse,
  DelegatedControlChargingStationResponseDto,
  EnergyOfferStatusResponse,
  ExtendedVehicleLinksResponseDto,
  FlexRequestResponse,
  GetApi3ChargeSchedules,
} from '@experience/shared/axios/smart-charging-service-client';
import { Charger } from '../charger';
import { ChargingStationProgrammesResponse } from '@experience/shared/axios/competitions-service-client';
import { ChargingStationSummary } from '@experience/shared/axios/assets-api-client';
import {
  ChargingStationTariffSearchResponseDto,
  SupplierDto,
} from '@experience/shared/axios/tariffs-api-client';
import { Charger as CommercialAttributes } from '@experience/shared/axios/internal-site-admin-api-client';
import { DataStatusResponse } from '@experience/shared/axios/connectivity-service-client';
import { DoorConfigurationResponse } from '@experience/shared/axios/assets-configuration-api-client';
import {
  FirmwareStatusType,
  FirmwareStatusTypeUpdateStatusStatusEnum,
} from '@experience/shared/axios/firmware-upgrade-client';
import {
  GetPcbSwaps200Response,
  GetWifiCredentials200Response,
} from '@experience/shared/axios/assets-provisioning-api-client';
import { InstallResponse } from '@experience/installer/api/axios';
import { List, Map, fromJS, mergeDeep } from 'immutable';
import { ListSubscriptionsDTO } from '@experience/mobile/subscriptions-api/axios';
import { ProjectionChargesResponse } from '@experience/shared/axios/data-platform-api-client';
import { SalesforceAsset } from '@experience/shared/salesforce/client';
import { UserInfoResponseDto } from '@experience/driver-account-api/api-client';
import { faker } from '@faker-js/faker';
import { getChargerControllerFindByIdentifier200Response } from '@experience/shared/axios/internal-site-admin-api-client-msw';
import {
  getGetActiveChargingStationFlexibilityRequests200Response,
  getGetChargingStationChargeSchedules200Response,
  getGetChargingStationEnergyOfferStatus200Response,
  getGetDelegatedControlChargingStationByPpid200Response,
  getGetDelegatedControlChargingStationVehicles200Response,
  getSearchActiveOverrides200Response,
} from '@experience/shared/axios/smart-charging-service-client-msw';
import { getGetChargingStation200Response } from '@experience/shared/axios/assets-api-client-msw';
import { getGetChargingStationProgrammes200Response } from '@experience/shared/axios/competitions-service-client-msw';
import { getGetChargingStationStatus200Response } from '@experience/shared/axios/connectivity-service-client-msw';
import { getGetCurrentFirmware200Response } from '@experience/shared/axios/firmware-upgrade-client-msw';
import { getGetDoorConfiguration200Response } from '@experience/shared/axios/assets-configuration-api-client-msw';
import {
  getGetPcbSwaps200Response,
  getGetWifiCredentials200Response,
} from '@experience/shared/axios/assets-provisioning-api-client-msw';
import {
  getGetSuppliers200Response,
  getGetTariffsByPpid200Response,
} from '@experience/shared/axios/tariffs-api-client-msw';
import { getInstallsControllerGetInstall200Response } from '@experience/installer/api/msw';
import { getProjectionChargesProjectionChargeData200Response } from '@experience/shared/axios/data-platform-api-client-msw';
import { getSubscriptionsControllerSearch200Response } from '@experience/mobile/subscriptions-api/msw';
import { getUserControllerGetByFilter200Response } from '@experience/driver-account-api/msw-client';

export const mockChargeHistory = Map(
  getProjectionChargesProjectionChargeData200Response()
)
  .setIn(['data', 0, 'startedAt'], '2024-06-30 18:00:00.000')
  .setIn(['data', 0, 'endedAt'], '2024-06-30 20:00:00.000')
  .setIn(['data', 1, 'startedAt'], '2024-06-30 18:00:00.000')
  .setIn(['data', 1, 'endedAt'], '2024-06-30 20:00:00.000')
  .toObject() as unknown as ProjectionChargesResponse;

export const mockChargeOverrides = List(
  getSearchActiveOverrides200Response()
).toArray() as unknown as ChargeOverrideResponse[];

export const mockChargeSchedules = Map(
  getGetChargingStationChargeSchedules200Response()
)
  .set('data', [
    {
      uid: faker.string.uuid(),
      start_day: 1,
      start_time: '10:00:00',
      end_day: 1,
      end_time: '14:00:00',
      status: { is_active: true },
    },
    {
      uid: faker.string.uuid(),
      start_day: 2,
      start_time: '20:35:00',
      end_day: 2,
      end_time: '23:50:00',
      status: { is_active: true },
    },
    {
      uid: faker.string.uuid(),
      start_day: 3,
      start_time: '00:00:00',
      end_day: 3,
      end_time: '05:00:00',
      status: { is_active: true },
    },
    {
      uid: faker.string.uuid(),
      start_day: 4,
      start_time: '16:10:00',
      end_day: 4,
      end_time: '17:10:00',
      status: { is_active: true },
    },
    {
      uid: faker.string.uuid(),
      start_day: 5,
      start_time: '00:00:00',
      end_day: 5,
      end_time: '05:00:00',
      status: { is_active: true },
    },
    {
      uid: faker.string.uuid(),
      start_day: 6,
      start_time: '00:00:00',
      end_day: 6,
      end_time: '05:00:00',
      status: { is_active: false },
    },
    {
      uid: faker.string.uuid(),
      start_day: 7,
      start_time: '00:00:00',
      end_day: 7,
      end_time: '05:00:00',
      status: { is_active: false },
    },
  ])
  .toObject() as unknown as GetApi3ChargeSchedules;

export const mockChargingStationProgrammes = Map(
  getGetChargingStationProgrammes200Response()
).toObject() as unknown as ChargingStationProgrammesResponse;

export const mockConfiguration = Map(getGetDoorConfiguration200Response())
  .setIn(
    ['data', 'configuration', 'configurationKey'],
    [
      { key: 'ChargeCurrentLimitA', value: '32' },
      { key: 'FirmwareVersion', value: '1.0.0' },
      { key: 'OffMode', value: 'false' },
      { key: 'OfflineSchedulingEnabled', value: 'true' },
      { key: 'LinkyScheduleEnabled', value: 'false' },
      { key: 'PPDevClampFaultThreshold', value: '2' },
      { key: 'PowerBalancingCurrentLimitImportA', value: '50' },
      { key: 'PowerBalancingEnabled', value: 'true' },
      { key: 'PowerBalancingSensor', value: 'NONE' },
      { key: 'PowerBalancingSensorInstalled', value: 'true' },
      { key: 'PowerBalancingSensorPolarityInverted', value: 'true' },
      { key: 'RcdBreakerSize', value: '10' },
      { key: 'SolarExportMargin', value: '5' },
      { key: 'SolarMatchingEnabled', value: 'true' },
      { key: 'SolarMaxGridImport', value: '4.1' },
      { key: 'SolarStartHysteresis', value: '10' },
      { key: 'SolarStopHysteresis', value: '64' },
      { key: 'SolarSystemInstalled', value: 'true' },
      { key: 'UnlockConnectorOnEVSideDisconnect', value: 'true' },
    ]
  )
  .toObject() as unknown as DoorConfigurationResponse;

export const mockConnectivityStatus = Map(
  getGetChargingStationStatus200Response()
)
  .setIn(['evses', 0, 'id'], 1)
  .setIn(['evses', 1, 'id'], 2)
  .setIn(
    ['evses', 0, 'connectivityState', 'lastMessageAt'],
    '2024-06-30 20:00:00.000'
  )
  .setIn(
    ['evses', 1, 'connectivityState', 'lastMessageAt'],
    '2024-06-31 09:30:00.000'
  )
  .setIn(
    ['evses', 0, 'connectivityState', 'lastSeenAt'],
    '2024-06-30 20:00:00.000'
  )
  .setIn(
    ['evses', 1, 'connectivityState', 'lastSeenAt'],
    '2024-06-31 09:30:00.000'
  )
  .setIn(
    ['evses', 0, 'connectors', 0, 'activeFaults', 0, 'vendorErrorCode'],
    'vendorErrorCode1'
  )
  .setIn(
    ['evses', 0, 'connectors', 0, 'activeFaults', 1, 'vendorErrorCode'],
    'vendorErrorCode2'
  )
  .setIn(
    ['evses', 0, 'connectors', 1, 'activeFaults', 0, 'vendorErrorCode'],
    'vendorErrorCode3'
  )
  .setIn(
    ['evses', 0, 'connectors', 1, 'activeFaults', 1, 'vendorErrorCode'],
    'vendorErrorCode4'
  )
  .setIn(
    ['evses', 1, 'connectors', 0, 'activeFaults', 0, 'vendorErrorCode'],
    'vendorErrorCode5'
  )
  .setIn(
    ['evses', 1, 'connectors', 0, 'activeFaults', 1, 'vendorErrorCode'],
    'vendorErrorCode6'
  )
  .setIn(
    ['evses', 1, 'connectors', 1, 'activeFaults', 0, 'vendorErrorCode'],
    'vendorErrorCode7'
  )
  .setIn(
    ['evses', 1, 'connectors', 1, 'activeFaults', 1, 'vendorErrorCode'],
    'vendorErrorCode8'
  )
  .toObject() as unknown as DataStatusResponse;

export const mockCommercialAttributes = Map(
  getChargerControllerFindByIdentifier200Response()
)
  .setIn(['group', 'name'], 'Test group')
  .setIn(['site', 'address', 'name'], 'Test site')
  .setIn(['site', 'contactDetails', 'email'], '<EMAIL>')
  .setIn(['site', 'contactDetails', 'name'], 'Test Contact')
  .setIn(['site', 'contactDetails', 'telephone'], '+44123456789')
  .setIn(['name'], 'Test-Name')
  .setIn(['tariff', 'name'], 'Test tariff')
  .setIn(['admins', 0, 'email'], '<EMAIL>')
  .toObject() as unknown as CommercialAttributes;

export const mockCurrentFirmware = List(getGetCurrentFirmware200Response())
  .setIn(
    [0, 'updateStatus', 'status'],
    FirmwareStatusTypeUpdateStatusStatusEnum.NotRequested
  )
  .toArray() as unknown as FirmwareStatusType[];

export const mockDelegatedControlStatus = Map(
  getGetDelegatedControlChargingStationByPpid200Response()
)
  .setIn(['statusEffectiveFrom'], '2024-06-30T20:00:00.000Z')
  .toObject() as unknown as DelegatedControlChargingStationResponseDto;

export const mockEnergyOffer = Map(
  getGetChargingStationEnergyOfferStatus200Response()
).toObject() as unknown as EnergyOfferStatusResponse;

export const mockFlexibilityRequests = List(
  getGetActiveChargingStationFlexibilityRequests200Response()
).toArray() as unknown as FlexRequestResponse[];

export const mockInstallationDetails = List(
  getInstallsControllerGetInstall200Response()
).toArray() as unknown as InstallResponse[];

export const mockLinkedUsers = fromJS(getUserControllerGetByFilter200Response())
  .map((user, index) =>
    index === 0 ? user : user.set('uid', faker.string.uuid())
  )
  .toJS() as UserInfoResponseDto[];

export const mockPcbHistory = Map(
  getGetPcbSwaps200Response()
).toObject() as GetPcbSwaps200Response;

export const mockSalesforceAsset: SalesforceAsset = {
  Warranty_Start_Date__c: '2024-08-01',
  Warranty_End_Date__c: '2027-08-01',
  Warranty_Status__c: 'Active',
};

export const mockSubscriptions = Map(
  getSubscriptionsControllerSearch200Response()
).toObject() as unknown as ListSubscriptionsDTO;

export const mockSummary = Map(getGetChargingStation200Response())
  .setIn(['location', 'type'], 'DOMESTIC')
  .toObject() as unknown as ChargingStationSummary;

export const mockTariffSuppliers = List(
  getGetSuppliers200Response()
).toArray() as unknown as SupplierDto[];

export const mockTariffs = Map(
  getGetTariffsByPpid200Response()
).toObject() as unknown as ChargingStationTariffSearchResponseDto;

export const mockCommercialChargerSummary = Map(
  getGetChargingStation200Response()
)
  .setIn(['location', 'type'], 'COMMERCIAL')
  .setIn(['ppid'], 'PG-12345')
  .toObject() as unknown as ChargingStationSummary;

export const mockWifiCredentials = Map(
  getGetWifiCredentials200Response()
).toObject() as GetWifiCredentials200Response;

export const mockVehicles = Map(
  getGetDelegatedControlChargingStationVehicles200Response()
).toObject() as unknown as ExtendedVehicleLinksResponseDto;

export const mockCharger: Charger = {
  chargeHistory: mockChargeHistory.data,
  chargeOverrides: mockChargeOverrides,
  chargeSchedules: mockChargeSchedules.data,
  chargingStationProgrammes: mockChargingStationProgrammes,
  configuration: mockConfiguration.data,
  connectivityStatus: mockConnectivityStatus,
  commercialAttributes: mockCommercialAttributes,
  currentFirmware: mockCurrentFirmware,
  delegatedControlStatus: mockDelegatedControlStatus,
  installationDetails: mockInstallationDetails,
  linkedUsers: [mockLinkedUsers[0]],
  name: mockCommercialAttributes.name,
  salesforceAsset: mockSalesforceAsset,
  subscriptions: mockSubscriptions.subscriptions,
  summary: mockSummary,
  tariffSuppliers: mockTariffSuppliers,
  tariffs: mockTariffs.data,
  vehicles: mockVehicles.data,
  wifiCredentials: mockWifiCredentials,
};

export const mockCommercialCharger: Charger = mergeDeep(mockCharger, {
  summary: {
    location: {
      type: 'COMMERCIAL',
    },
    ppid: 'PG-12345',
  },
  chargeHistory: [
    mockChargeHistory.data[0],
    { ...mockChargeHistory.data[1], door: 'B' },
  ],
});
