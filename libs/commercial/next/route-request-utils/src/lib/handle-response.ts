import { notFound } from 'next/navigation';

export enum StatusCode {
  HTTP_200 = 200,
  HTTP_201 = 201,
  HTTP_202 = 202,
  HTTP_204 = 204,
  HTTP_400 = 400,
  HTTP_403 = 403,
  HTTP_404 = 404,
  HTTP_409 = 409,
  HTTP_422 = 422,
  HTTP_500 = 500,
}

export type CustomResponseHandlers<T> = {
  [key in StatusCode]?: (response: Response) => Promise<T>;
};

export const handleResponse = async <T>(
  response: Response,
  customHandlers?: CustomResponseHandlers<T>
): Promise<T> => {
  const status = response.status as StatusCode;

  if (customHandlers?.[status]) {
    return customHandlers[status](response);
  }

  if (response.status === StatusCode.HTTP_404) {
    return notFound();
  }

  if (
    [
      StatusCode.HTTP_200,
      StatusCode.HTTP_201,
      StatusCode.HTTP_202,
      StatusCode.HTTP_204,
    ].includes(response.status)
  ) {
    return getOkResponseBody<T>(response);
  }

  throw new Error(await getErrorResponseBody(response));
};

const getOkResponseBody = async <T>(response: Response): Promise<T> => {
  try {
    return await response.json();
  } catch (error) {
    return response.statusText as T;
  }
};

const getErrorResponseBody = async (response: Response): Promise<string> => {
  try {
    return (await response.json())?.message ?? response.statusText;
  } catch (error) {
    return response.statusText;
  }
};
