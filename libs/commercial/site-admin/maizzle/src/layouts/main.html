<!DOCTYPE {{{ page.doctype || 'html' }}}>
<html
  lang="{{ page.language || 'en' }}"
  xmlns:v="urn:schemas-microsoft-com:vml"
>
  <head>
    <meta charset="{{ page.charset || 'utf-8' }}" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="format-detection"
      content="telephone=no, date=no, address=no, email=no, url=no"
    />
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings
            xmlns:o="urn:schemas-microsoft-com:office:office"
          >
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: 'TT Commons Pro', Arial, sans-serif;
          mso-line-height-rule: exactly;
        }
      </style>
    <![endif]-->
    <if condition="page.title">
      <title>{{{ page.title }}}</title>
    </if>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=TT+Commons+Pro:wght@400;600;700&display=swap"
      rel="stylesheet"
      media="screen"
    />

    <style>
      @tailwind components;
      @tailwind utilities;

      /* Email-specific styles */
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
      }

      .email-header {
        background-color: #163E4C;
        padding: 48px 20px;
        text-align: center;
      }

      .email-content {
        padding: 40px 20px;
        background-color: #ffffff;
      }

      .email-footer {
        background-color: #000000;
        color: #ffffff;
        padding: 40px 20px;
        text-align: center;
      }

      .pod-logo {
        width: 120px;
        height: auto;
        margin: 0 auto 16px;
      }

      .email-title {
        color: #ffdc50;
        font-size: 24px;
        font-weight: 700;
        margin: 0;
        line-height: 1.2;
      }

      .email-body {
        font-family: 'TT Commons Pro', Arial, sans-serif;
        font-size: 16px;
        line-height: 1.5;
        color: #32322d;
      }

      .email-button {
        background-color: #000000;
        color: #ffffff;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 4px;
        display: inline-block;
        font-weight: 600;
        margin: 20px 0;
      }

      .email-button:hover {
        background-color: #32322d;
      }

      .footer-logo {
        width: 80px;
        height: auto;
        margin: 0 auto 20px;
      }

      .footer-text {
        font-size: 14px;
        color: #ffffff;
        margin: 8px 0;
      }

      .footer-links {
        margin: 20px 0;
      }

      .footer-link {
        color: #ffffff;
        text-decoration: none;
        margin: 0 10px;
        font-size: 14px;
      }
    </style>
  </head>
  <body
    class="m-0 p-0 w-full [word-break:break-word] [-webkit-font-smoothing:antialiased] bg-gray-100 {{ page.bodyClass || '' }}"
  >
    <if condition="page.preheader">
      <div class="hidden">
        {{{ page.preheader }}}
        <each loop="item in Array.from(Array(150))">&#847; </each>
      </div>
    </if>

    <div
      role="article"
      aria-roledescription="email"
      aria-label="{{{ page.title || '' }}}"
      lang="{{ page.language || 'en' }}"
      class="email-container"
    >
      <!-- Email Header -->
      <div class="email-header">
        <!-- Pod Point Logo -->
        <div class="pod-logo">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 400 120"
            style="width: 160px; height: auto"
          >
            <g fill="#ffffff">
              <!-- Pod Point Logo SVG -->
              <!-- "p" letter -->
              <path d="M40 20 L40 100 L50 100 L50 65 L75 65 C90 65 100 55 100 45 C100 35 90 25 75 25 L40 25 Z M50 35 L75 35 C82 35 90 40 90 45 C90 50 82 55 75 55 L50 55 Z"/>

              <!-- "o" letter -->
              <circle cx="125" cy="60" r="25" fill="none" stroke="#ffffff" stroke-width="10"/>
              <circle cx="125" cy="60" r="8" fill="#ffffff"/>

              <!-- "d" letter -->
              <path d="M180 20 L180 100 L190 100 L190 65 L215 65 C230 65 240 55 240 45 C240 35 230 25 215 25 L180 25 Z M190 35 L215 35 C222 35 230 40 230 45 C230 50 222 55 215 55 L190 55 Z"/>
              <rect x="235" y="20" width="10" height="80" fill="#ffffff"/>

              <!-- "Point" text -->
              <text x="280" y="50" style="font-family: 'TT Commons Pro', Arial, sans-serif; font-size: 24px; font-weight: 700; fill: #ffffff;">Point</text>
            </g>
          </svg>
        </div>

        <!-- Email Title -->
        <if condition="page.emailTitle">
          <h1 class="email-title">{{{ page.emailTitle }}}</h1>
        </if>
      </div>

      <!-- Email Content -->
      <div class="email-content email-body">
        <yield />
      </div>

      <!-- Email Footer -->
      <div class="email-footer">
        <!-- Footer Logo -->
        <div class="footer-logo">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 160 80"
            style="width: 120px; height: auto"
          >
            <g>
              <!-- Pod Point "Power of the People" Lockup -->
              <!-- Main Pod Point text -->
              <text
                x="80"
                y="25"
                text-anchor="middle"
                style="
                  font-family: 'TT Commons Pro', Arial, sans-serif;
                  font-size: 16px;
                  font-weight: 700;
                  fill: #ffffff;
                  letter-spacing: 0.5px;
                "
              >
                Pod Point
              </text>
              <!-- Connecting element -->
              <g transform="translate(65, 35)">
                <circle cx="5" cy="5" r="2" fill="#ffffff"/>
                <circle cx="15" cy="5" r="2" fill="#ffffff"/>
                <circle cx="25" cy="5" r="2" fill="#ffffff"/>
                <path d="M3,5 Q15,1 27,5" stroke="#ffdc50" stroke-width="1.5" fill="none"/>
              </g>
              <!-- "Power of the People" tagline -->
              <text
                x="80"
                y="55"
                text-anchor="middle"
                style="
                  font-family: 'TT Commons Pro', Arial, sans-serif;
                  font-size: 10px;
                  font-weight: 400;
                  fill: #ffffff;
                  letter-spacing: 0.3px;
                "
              >
                Power of the People
              </text>
            </g>
          </svg>
        </div>

        <div class="footer-text">Power of the People</div>

        <div class="footer-links">
          <a
            href="https://pod-point.com/legal/privacy-and-cookies-policy"
            class="footer-link"
            >Privacy Notice</a
          >
          <a href="https://pod-point.com/legal/policies" class="footer-link"
            >Terms & Conditions</a
          >
        </div>

        <div class="footer-text">
          Questions? Call our customer service team on 020 7247 4114
        </div>
        <div class="footer-text">
          Registered Office: 222 Gray's Inn Road, London, WC1X 8HB
        </div>
        <div class="footer-text">
          © Pod Point Limited {{ new Date().getFullYear() }}
        </div>
      </div>
    </div>
  </body>
</html>
