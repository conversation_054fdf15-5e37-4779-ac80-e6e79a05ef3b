<!DOCTYPE {{{ page.doctype || 'html' }}}>
<html
  lang="{{ page.language || 'en' }}"
  xmlns:v="urn:schemas-microsoft-com:vml"
>
  <head>
    <meta charset="{{ page.charset || 'utf-8' }}" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="format-detection"
      content="telephone=no, date=no, address=no, email=no, url=no"
    />
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings
            xmlns:o="urn:schemas-microsoft-com:office:office"
          >
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: 'Quicksand', sans-serif;
          mso-line-height-rule: exactly;
        }
      </style>
    <![endif]-->
    <if condition="page.title">
      <title>{{{ page.title }}}</title>
    </if>

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Quicksand:wght@400;600;700&display=swap"
      rel="stylesheet"
      media="screen"
    />

    <style>
      @tailwind components;
      @tailwind utilities;

      /* Email-specific styles */
      .email-container {
        max-width: 600px;
        margin: 0 auto;
        background-color: #ffffff;
      }

      .email-header {
        background-color: #005f41;
        padding: 48px 20px;
        text-align: center;
      }

      .email-content {
        padding: 40px 20px;
        background-color: #ffffff;
      }

      .email-footer {
        background-color: #000000;
        color: #ffffff;
        padding: 40px 20px;
        text-align: center;
      }

      .pod-logo {
        width: 120px;
        height: auto;
        margin: 0 auto 16px;
      }

      .email-title {
        color: #ffdc50;
        font-size: 24px;
        font-weight: 700;
        margin: 0;
        line-height: 1.2;
      }

      .email-body {
        font-family: 'Quicksand', sans-serif;
        font-size: 16px;
        line-height: 1.5;
        color: #32322d;
      }

      .email-button {
        background-color: #000000;
        color: #ffffff;
        padding: 12px 24px;
        text-decoration: none;
        border-radius: 4px;
        display: inline-block;
        font-weight: 600;
        margin: 20px 0;
      }

      .email-button:hover {
        background-color: #32322d;
      }

      .footer-logo {
        width: 80px;
        height: auto;
        margin: 0 auto 20px;
      }

      .footer-text {
        font-size: 14px;
        color: #ffffff;
        margin: 8px 0;
      }

      .footer-links {
        margin: 20px 0;
      }

      .footer-link {
        color: #ffffff;
        text-decoration: none;
        margin: 0 10px;
        font-size: 14px;
      }
    </style>
  </head>
  <body
    class="m-0 p-0 w-full [word-break:break-word] [-webkit-font-smoothing:antialiased] bg-gray-100 {{ page.bodyClass || '' }}"
  >
    <if condition="page.preheader">
      <div class="hidden">
        {{{ page.preheader }}}
        <each loop="item in Array.from(Array(150))">&#847; </each>
      </div>
    </if>

    <div
      role="article"
      aria-roledescription="email"
      aria-label="{{{ page.title || '' }}}"
      lang="{{ page.language || 'en' }}"
      class="email-container"
    >
      <!-- Email Header -->
      <div class="email-header">
        <!-- Pod Energy Logo -->
        <div class="pod-logo">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 200 80"
            style="width: 120px; height: auto"
          >
            <g>
              <!-- Circular background -->
              <circle cx="40" cy="40" r="35" fill="#ffffff" />
              <!-- "pd" text -->
              <text
                x="40"
                y="50"
                text-anchor="middle"
                style="
                  font-family: 'Quicksand', sans-serif;
                  font-size: 24px;
                  font-weight: 700;
                  fill: #005f41;
                "
              >
                pd
              </text>
              <!-- "energy" text -->
              <text
                x="120"
                y="35"
                text-anchor="middle"
                style="
                  font-family: 'Quicksand', sans-serif;
                  font-size: 16px;
                  font-weight: 600;
                  fill: #ffffff;
                "
              >
                Pod energy
              </text>
              <text
                x="120"
                y="55"
                text-anchor="middle"
                style="
                  font-family: 'Quicksand', sans-serif;
                  font-size: 10px;
                  font-weight: 400;
                  fill: #ffffff;
                "
              >
                Power of the People
              </text>
            </g>
          </svg>
        </div>

        <!-- Email Title -->
        <if condition="page.emailTitle">
          <h1 class="email-title">{{{ page.emailTitle }}}</h1>
        </if>
      </div>

      <!-- Email Content -->
      <div class="email-content email-body">
        <yield />
      </div>

      <!-- Email Footer -->
      <div class="email-footer">
        <!-- Footer Logo -->
        <div class="footer-logo">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 80 80"
            style="width: 60px; height: auto"
          >
            <g>
              <!-- Circular background -->
              <circle cx="40" cy="40" r="35" fill="#ffffff" />
              <!-- "pd" text -->
              <text
                x="40"
                y="50"
                text-anchor="middle"
                style="
                  font-family: 'Quicksand', sans-serif;
                  font-size: 20px;
                  font-weight: 700;
                  fill: #000000;
                "
              >
                pd
              </text>
            </g>
          </svg>
        </div>

        <div class="footer-text">Power of the People</div>

        <div class="footer-links">
          <a
            href="https://pod-point.com/legal/privacy-and-cookies-policy"
            class="footer-link"
            >Privacy Notice</a
          >
          <a href="https://pod-point.com/legal/policies" class="footer-link"
            >Terms & Conditions</a
          >
        </div>

        <div class="footer-text">
          Questions? Call our customer service team on 020 7247 4114
        </div>
        <div class="footer-text">
          Registered Office: 222 Gray's Inn Road, London, WC1X 8HB
        </div>
        <div class="footer-text">
          © Pod Point Limited {{ new Date().getFullYear() }}
        </div>
      </div>
    </div>
  </body>
</html>
