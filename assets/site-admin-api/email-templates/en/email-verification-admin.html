<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
  <head>
    <meta charset="utf-8" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="format-detection"
      content="telephone=no, date=no, address=no, email=no, url=no"
    />
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings
            xmlns:o="urn:schemas-microsoft-com:office:office"
          >
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: 'TT Commons Pro', Arial, sans-serif;
          mso-line-height-rule: exactly;
        }
      </style>
    <![endif]-->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=TT+Commons+Pro:wght@400;600;700&display=swap"
      rel="stylesheet"
      media="screen"
    />
    <style>
      .email-button:hover {
        background-color: #32322d;
      }
    </style>
  </head>
  <body
    style="
      margin: 0;
      width: 100%;
      background-color: #f3f4f6;
      padding: 0;
      -webkit-font-smoothing: antialiased;
      word-break: break-word;
    "
  >
    <div
      role="article"
      aria-roledescription="email"
      aria-label
      lang="en"
      style="max-width: 600px; margin: 0 auto; background-color: #ffffff"
    >
      <div
        style="
          background-color: #163e4c;
          padding: 48px 20px;
          text-align: center;
        "
      >
        <div style="width: 120px; height: auto; margin: 0 auto 16px">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 200 60"
            style="width: 160px; height: auto"
          >
            <g>
              <text
                x="100"
                y="20"
                text-anchor="middle"
                style="
                  font-family: 'TT Commons Pro', Arial, sans-serif;
                  font-size: 18px;
                  font-weight: 700;
                  fill: #ffffff;
                  letter-spacing: 0.5px;
                "
              >
                Pod Point
              </text>
              <g transform="translate(85, 30)">
                <circle cx="5" cy="8" r="3" fill="#ffffff"></circle>
                <circle cx="15" cy="8" r="3" fill="#ffffff"></circle>
                <circle cx="25" cy="8" r="3" fill="#ffffff"></circle>
                <path
                  d="M2,8 Q10,2 18,8 Q26,2 28,8"
                  stroke="#ffdc50"
                  stroke-width="2"
                  fill="none"
                ></path>
              </g>
            </g>
          </svg>
        </div>
        <h1
          style="
            color: #ffdc50;
            font-size: 24px;
            font-weight: 700;
            margin: 0;
            line-height: 1.2;
          "
        >
          Verify your email
        </h1>
      </div>
      <div
        style="
          padding: 40px 20px;
          background-color: #ffffff;
          font-family: 'TT Commons Pro', Arial, sans-serif;
          font-size: 16px;
          line-height: 1.5;
          color: #32322d;
        "
      >
        <p style="margin-bottom: 24px">Hi [name],</p>
        <p style="margin-bottom: 24px">
          We have sent you this email as we received a request to verify your
          email address for your {{service}} account.
        </p>
        <p style="margin-bottom: 24px">
          Please click the button below to verify your email address:
        </p>
        <div style="margin-bottom: 24px; text-align: center">
          <a
            href="https://{{actionUrl}}"
            class="email-button"
            style="
              background-color: #000000;
              color: #ffffff;
              padding: 12px 24px;
              text-decoration: none;
              border-radius: 4px;
              display: inline-block;
              font-weight: 600;
              margin: 20px 0;
            "
          >
            Verify your email
          </a>
        </div>
        <p style="margin-bottom: 24px">
          If you did not request this verification, no further action is
          required.
        </p>
        <p style="margin-bottom: 24px">
          If you have any issues or the button doesn't work, you can also copy
          and paste this link into your browser:
        </p>
        <p
          style="
            margin-bottom: 24px;
            word-break: break-all;
            font-size: 14px;
            color: #4b5563;
          "
        >
          https://{{actionUrl}}
        </p>
        <div style="margin-top: 32px">
          <p style="margin-bottom: 8px; font-weight: 600">Thank you!</p>
          <p style="margin-bottom: 0">The Pod Team</p>
        </div>
      </div>
      <div
        style="
          background-color: #000000;
          color: #ffffff;
          padding: 40px 20px;
          text-align: center;
        "
      >
        <div style="width: 80px; height: auto; margin: 0 auto 20px">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 160 80"
            style="width: 120px; height: auto"
          >
            <g>
              <text
                x="80"
                y="25"
                text-anchor="middle"
                style="
                  font-family: 'TT Commons Pro', Arial, sans-serif;
                  font-size: 16px;
                  font-weight: 700;
                  fill: #ffffff;
                  letter-spacing: 0.5px;
                "
              >
                Pod Point
              </text>
              <g transform="translate(65, 35)">
                <circle cx="5" cy="5" r="2" fill="#ffffff"></circle>
                <circle cx="15" cy="5" r="2" fill="#ffffff"></circle>
                <circle cx="25" cy="5" r="2" fill="#ffffff"></circle>
                <path
                  d="M3,5 Q15,1 27,5"
                  stroke="#ffdc50"
                  stroke-width="1.5"
                  fill="none"
                ></path>
              </g>
              <text
                x="80"
                y="55"
                text-anchor="middle"
                style="
                  font-family: 'TT Commons Pro', Arial, sans-serif;
                  font-size: 10px;
                  font-weight: 400;
                  fill: #ffffff;
                  letter-spacing: 0.3px;
                "
              >
                Power of the People
              </text>
            </g>
          </svg>
        </div>
        <div style="font-size: 14px; color: #ffffff; margin: 8px 0">
          Power of the People
        </div>
        <div style="margin: 20px 0">
          <a
            href="https://pod-point.com/legal/privacy-and-cookies-policy"
            style="
              color: #ffffff;
              text-decoration: none;
              margin: 0 10px;
              font-size: 14px;
            "
            >Privacy Notice</a
          >
          <a
            href="https://pod-point.com/legal/policies"
            style="
              color: #ffffff;
              text-decoration: none;
              margin: 0 10px;
              font-size: 14px;
            "
            >Terms & Conditions</a
          >
        </div>
        <div style="font-size: 14px; color: #ffffff; margin: 8px 0">
          Questions? Call our customer service team on 020 7247 4114
        </div>
        <div style="font-size: 14px; color: #ffffff; margin: 8px 0">
          Registered Office: 222 Gray's Inn Road, London, WC1X 8HB
        </div>
        <div style="font-size: 14px; color: #ffffff; margin: 8px 0">
          © Pod Point Limited 2025
        </div>
      </div>
    </div>
  </body>
</html>
