<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
  <head>
    <meta charset="utf-8" />
    <meta name="x-apple-disable-message-reformatting" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta
      name="format-detection"
      content="telephone=no, date=no, address=no, email=no, url=no"
    />
    <meta name="color-scheme" content="light dark" />
    <meta name="supported-color-schemes" content="light dark" />
    <!--[if mso]>
      <noscript>
        <xml>
          <o:OfficeDocumentSettings
            xmlns:o="urn:schemas-microsoft-com:office:office"
          >
            <o:PixelsPerInch>96</o:PixelsPerInch>
          </o:OfficeDocumentSettings>
        </xml>
      </noscript>
      <style>
        td,
        th,
        div,
        p,
        a,
        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          font-family: 'TT Commons Pro', Arial, sans-serif;
          mso-line-height-rule: exactly;
        }
      </style>
    <![endif]-->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=TT+Commons+Pro:wght@400;600;700&display=swap"
      rel="stylesheet"
      media="screen"
    />
    <style>
      .email-button:hover {
        background-color: #32322d;
      }
    </style>
  </head>
  <body
    style="
      margin: 0;
      width: 100%;
      background-color: #f3f4f6;
      padding: 0;
      -webkit-font-smoothing: antialiased;
      word-break: break-word;
    "
  >
    <div
      role="article"
      aria-roledescription="email"
      aria-label
      lang="en"
      style="max-width: 600px; margin: 0 auto; background-color: #ffffff"
    >
      <div
        style="
          background-color: #163e4c;
          padding: 48px 20px;
          text-align: center;
        "
      >
        <div style="width: 120px; height: auto; margin: 0 auto 16px">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 300 80"
            style="width: 160px; height: auto"
          >
            <g fill="#ffffff" stroke="#ffffff">
              <path
                d="M20 15 L20 65 L25 65 L25 45 L35 45 C42 45 47 40 47 32.5 C47 25 42 20 35 20 L20 20 Z M25 25 L35 25 C38 25 42 27 42 32.5 C42 38 38 40 35 40 L25 40 Z"
                stroke-width="0"
              ></path>
              <circle
                cx="65"
                cy="40"
                r="15"
                fill="none"
                stroke="#ffffff"
                stroke-width="5"
              ></circle>
              <circle
                cx="65"
                cy="40"
                r="5"
                fill="#ffffff"
                stroke-width="0"
              ></circle>
              <circle
                cx="85"
                cy="50"
                r="8"
                fill="none"
                stroke="#ffffff"
                stroke-width="5"
              ></circle>
              <circle
                cx="85"
                cy="50"
                r="3"
                fill="#ffffff"
                stroke-width="0"
              ></circle>
              <path
                d="M110 15 L110 65 L115 65 L115 45 L125 45 C132 45 137 40 137 32.5 C137 25 132 20 125 20 L110 20 Z M115 25 L125 25 C128 25 132 27 132 32.5 C132 38 128 40 125 40 L115 40 Z"
                stroke-width="0"
              ></path>
              <rect
                x="140"
                y="15"
                width="5"
                height="50"
                fill="#ffffff"
                stroke-width="0"
              ></rect>
              <text
                x="170"
                y="35"
                style="
                  font-family: 'TT Commons Pro', Arial, sans-serif;
                  font-size: 18px;
                  font-weight: 700;
                  fill: #ffffff;
                "
              >
                Point
              </text>
            </g>
          </svg>
        </div>
        <h1
          style="
            color: #ffdc50;
            font-size: 24px;
            font-weight: 700;
            margin: 0;
            line-height: 1.2;
          "
        >
          Reset your password
        </h1>
      </div>
      <div
        style="
          padding: 40px 20px;
          background-color: #ffffff;
          font-family: 'TT Commons Pro', Arial, sans-serif;
          font-size: 16px;
          line-height: 1.5;
          color: #32322d;
        "
      >
        <p style="margin-bottom: 24px">Hi [name],</p>
        <p style="margin-bottom: 24px">
          We received a request to reset the password for your {{service}}
          account ({{emailAddress}}).
        </p>
        <p style="margin-bottom: 24px">
          Click the button below to reset your password:
        </p>
        <div style="margin-bottom: 24px; text-align: center">
          <a
            href="https://{{actionUrl}}"
            class="email-button"
            style="
              background-color: #000000;
              color: #ffffff;
              padding: 12px 24px;
              text-decoration: none;
              border-radius: 4px;
              display: inline-block;
              font-weight: 600;
              margin: 20px 0;
            "
          >
            Reset your password
          </a>
        </div>
        <p style="margin-bottom: 24px">
          If you did not request a password reset, please ignore this email.
          Your password will remain unchanged.
        </p>
        <p style="margin-bottom: 24px">
          If you have any issues or the button doesn't work, you can also copy
          and paste this link into your browser:
        </p>
        <p
          style="
            margin-bottom: 24px;
            word-break: break-all;
            font-size: 14px;
            color: #4b5563;
          "
        >
          https://{{actionUrl}}
        </p>
        <div style="margin-top: 32px">
          <p style="margin-bottom: 8px; font-weight: 600">Thank you!</p>
          <p style="margin-bottom: 0">The Pod Team</p>
        </div>
      </div>
      <div
        style="
          background-color: #000000;
          color: #ffffff;
          padding: 40px 20px;
          text-align: center;
        "
      >
        <div style="width: 80px; height: auto; margin: 0 auto 20px">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 200 100"
            style="width: 120px; height: auto"
          >
            <g fill="#ffffff" stroke="#ffffff">
              <path
                d="M40 20 L40 50 L43 50 L43 38 L48 38 C52 38 55 35 55 31 C55 27 52 24 48 24 L40 24 Z M43 27 L48 27 C50 27 52 28 52 31 C52 34 50 35 48 35 L43 35 Z"
                stroke-width="0"
              ></path>
              <circle
                cx="65"
                cy="32"
                r="8"
                fill="none"
                stroke="#ffffff"
                stroke-width="3"
              ></circle>
              <circle
                cx="65"
                cy="32"
                r="3"
                fill="#ffffff"
                stroke-width="0"
              ></circle>
              <circle
                cx="78"
                cy="38"
                r="5"
                fill="none"
                stroke="#ffffff"
                stroke-width="3"
              ></circle>
              <circle
                cx="78"
                cy="38"
                r="2"
                fill="#ffffff"
                stroke-width="0"
              ></circle>
              <path
                d="M95 20 L95 50 L98 50 L98 38 L103 38 C107 38 110 35 110 31 C110 27 107 24 103 24 L95 24 Z M98 27 L103 27 C105 27 107 28 107 31 C107 34 105 35 103 35 L98 35 Z"
                stroke-width="0"
              ></path>
              <rect
                x="112"
                y="20"
                width="3"
                height="30"
                fill="#ffffff"
                stroke-width="0"
              ></rect>
              <text
                x="130"
                y="32"
                style="
                  font-family: 'TT Commons Pro', Arial, sans-serif;
                  font-size: 12px;
                  font-weight: 700;
                  fill: #ffffff;
                "
              >
                Point
              </text>
              <text
                x="100"
                y="70"
                text-anchor="middle"
                style="
                  font-family: 'TT Commons Pro', Arial, sans-serif;
                  font-size: 10px;
                  font-weight: 400;
                  fill: #ffffff;
                "
              >
                Power of the People
              </text>
            </g>
          </svg>
        </div>
        <div style="font-size: 14px; color: #ffffff; margin: 8px 0">
          Power of the People
        </div>
        <div style="margin: 20px 0">
          <a
            href="https://pod-point.com/legal/privacy-and-cookies-policy"
            style="
              color: #ffffff;
              text-decoration: none;
              margin: 0 10px;
              font-size: 14px;
            "
            >Privacy Notice</a
          >
          <a
            href="https://pod-point.com/legal/policies"
            style="
              color: #ffffff;
              text-decoration: none;
              margin: 0 10px;
              font-size: 14px;
            "
            >Terms & Conditions</a
          >
        </div>
        <div style="font-size: 14px; color: #ffffff; margin: 8px 0">
          Questions? Call our customer service team on 020 7247 4114
        </div>
        <div style="font-size: 14px; color: #ffffff; margin: 8px 0">
          Registered Office: 222 Gray's Inn Road, London, WC1X 8HB
        </div>
        <div style="font-size: 14px; color: #ffffff; margin: 8px 0">
          © Pod Point Limited 2025
        </div>
      </div>
    </div>
  </body>
</html>
